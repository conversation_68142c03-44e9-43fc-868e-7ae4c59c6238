# Copyright (c) 2025 SciKnowOrg
#
# Permission is hereby granted, free of charge, to any person obtaining a copy
# of this software and associated documentation files (the "Software"), to deal
# in the Software without restriction, including without limitation the rights
# to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
# copies of the Software, and to permit persons to whom the Software is
# furnished to do so, subject to the following conditions:
#
# The above copyright notice and this permission notice shall be included in all
# copies or substantial portions of the Software.
#
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
# IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
# FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
# AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
# LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
# OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
# SOFTWARE.

from ..base.ontology import BaseOntology


class Hydra(BaseOntology):
    """
    Hydra is a lightweight vocabulary to create hypermedia-driven Web APIs. By specifying a number of concepts
    commonly used in Web APIs it enables the creation of generic API clients.
    """
    ontology_id = "Hydra"
    ontology_full_name = "Hydra Ontology (Hydra)"
    domain = "Web and Internet"
    category = "Web Development"
    version = None
    last_updated = "13 July 2021"
    creator = "Hydra W3C Community Group"
    license = "Creative Commons 4.0"
    format = "JSONLD"
    download_url = "https://www.hydra-cg.com/spec/latest/core/#references"


class SAREF(BaseOntology):
    """
    The Smart Applications REFerence (SAREF) suite of ontologies forms a shared model of consensus
    intended to enable semantic interoperability between solutions from different providers
    and among various activity sectors in the Internet of Things (IoT),
    thus contributing to the development of data spaces. SAREF is published as a set of open standards
    produced by ETSI Technical Committee Smart Machine-to-Machine communications (SmartM2M).
    """
    ontology_id = "SAREF"
    ontology_full_name = "Smart Applications REFerence ontology (SAREF)"
    domain = "Web and Internet"
    category = "interoperability"
    version = "3.2.1"
    last_updated = "2020-12-31"
    creator = "ETSI Technical Committee Smart Machine-to-Machine communications (SmartM2M)"
    license = None
    format = "RDF"
    download_url = "https://saref.etsi.org/core/v3.2.1/"
