# Copyright (c) 2023-2025 Scientific Knowledge Organization (SciKnowOrg)
#
# Permission is hereby granted, free of charge, to any person obtaining a copy
# of this software and associated documentation files (the "Software"), to deal
# in the Software without restriction, including without limitation the rights
# to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
# copies of the Software, and to permit persons to whom the Software is
# furnished to do so, subject to the following conditions:
#
# The above copyright notice and this permission notice shall be included in all
# copies or substantial portions of the Software.
#
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
# IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
# FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
# AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
# LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
# OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
# SOFTWARE.

"""
Script to add MIT license headers to Python files in the OntoLearner project.

This script automatically adds the MIT license header to Python files that don't
already have a license header. It skips files that already contain copyright notices.
"""

import os
import logging
from pathlib import Path
from typing import List

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# MIT License header template
MIT_LICENSE_HEADER = '''# Copyright (c) 2023-2025 Scientific Knowledge Organization (SciKnowOrg)
#
# Permission is hereby granted, free of charge, to any person obtaining a copy
# of this software and associated documentation files (the "Software"), to deal
# in the Software without restriction, including without limitation the rights
# to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
# copies of the Software, and to permit persons to whom the Software is
# furnished to do so, subject to the following conditions:
#
# The above copyright notice and this permission notice shall be included in all
# copies or substantial portions of the Software.
#
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
# IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
# FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
# AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
# LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
# OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
# SOFTWARE.

'''


def has_license_header(file_path: Path) -> bool:
    """
    Check if a Python file already has a license header.
    
    Args:
        file_path (Path): Path to the Python file
        
    Returns:
        bool: True if the file has a license header, False otherwise
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read(500)  # Read first 500 characters
            return 'Copyright' in content or 'LICENSE' in content or 'License' in content
    except Exception as e:
        logger.error(f"Error reading file {file_path}: {e}")
        return True  # Assume it has a header to avoid modifying problematic files


def add_license_header(file_path: Path) -> bool:
    """
    Add MIT license header to a Python file.
    
    Args:
        file_path (Path): Path to the Python file
        
    Returns:
        bool: True if header was added successfully, False otherwise
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            original_content = f.read()
        
        # Add license header at the beginning
        new_content = MIT_LICENSE_HEADER + original_content
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        logger.info(f"Added license header to {file_path}")
        return True
    except Exception as e:
        logger.error(f"Error adding license header to {file_path}: {e}")
        return False


def find_python_files(directory: Path) -> List[Path]:
    """
    Find all Python files in a directory recursively.
    
    Args:
        directory (Path): Directory to search
        
    Returns:
        List[Path]: List of Python file paths
    """
    python_files = []
    for root, dirs, files in os.walk(directory):
        # Skip certain directories
        dirs[:] = [d for d in dirs if d not in ['.git', '__pycache__', '.pytest_cache', 'build', 'dist']]
        
        for file in files:
            if file.endswith('.py'):
                python_files.append(Path(root) / file)
    
    return python_files


def main():
    """Main function to add license headers to Python files."""
    # Get the project root directory
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    
    # Find all Python files in the ontolearner package
    ontolearner_dir = project_root / 'ontolearner'
    if not ontolearner_dir.exists():
        logger.error(f"OntoLearner directory not found: {ontolearner_dir}")
        return
    
    python_files = find_python_files(ontolearner_dir)
    logger.info(f"Found {len(python_files)} Python files in {ontolearner_dir}")
    
    # Process each file
    files_modified = 0
    files_skipped = 0
    
    for file_path in python_files:
        if has_license_header(file_path):
            logger.debug(f"Skipping {file_path} (already has license header)")
            files_skipped += 1
        else:
            if add_license_header(file_path):
                files_modified += 1
    
    logger.info(f"Processing complete:")
    logger.info(f"  Files modified: {files_modified}")
    logger.info(f"  Files skipped: {files_skipped}")
    logger.info(f"  Total files: {len(python_files)}")


if __name__ == "__main__":
    main()
