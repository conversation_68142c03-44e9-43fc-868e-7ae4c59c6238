# Copyright (c) 2023-2025 Scientific Knowledge Organization (SciKnowOrg)
#
# Permission is hereby granted, free of charge, to any person obtaining a copy
# of this software and associated documentation files (the "Software"), to deal
# in the Software without restriction, including without limitation the rights
# to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
# copies of the Software, and to permit persons to whom the Software is
# furnished to do so, subject to the following conditions:
#
# The above copyright notice and this permission notice shall be included in all
# copies or substantial portions of the Software.
#
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
# IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
# FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
# AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
# LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
# OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
# SOFTWARE.

from ..base import BaseOntology


class GEO(BaseOntology):
    """
    Geographical Entities Ontology (GEO) is an inventory of geopolitical entities (such as sovereign states
    and their administrative subdivisions) as well as various geographical regions (including but not limited
    to the specific ones over which the governments have jurisdiction)
    """
    ontology_id = "GEO"
    ontology_full_name = "Geographical Entities Ontology (GEO)"
    domain = "Geography"
    category = "Geographic Knowledge"
    version = None
    last_updated = "2019-02-17"
    creator = "William R Hogan"
    license = "Creative Commons 4.0"
    format = "OWL"
    download_url = "https://github.com/mcwdsi/geographical-entity-ontology/blob/master/geo-all.owl"

    def contains_imports(self) -> bool:
        """Hook: Check if the ontology contains imports."""
        return True


class GeoNames(BaseOntology):
    """
    The Geonames ontologies provides elements of description for geographical features,
    in particular those defined in the geonames.org database.
    """
    ontology_id = "GeoNames"
    ontology_full_name = "GeoNames Ontology (GeoNames)"
    domain = "Geography"
    category = "Geographic Knowledge"
    version = "3.3"
    last_updated = "2022-01-30"
    creator = "Bernard Vatant"
    license = "Creative Commons 3.0"
    format = "RDF"
    download_url = "https://www.geonames.org/ontology"


class GTS(BaseOntology):
    """
    This is an RDF/OWL representation of the GeoSciML Geologic Timescale model, which has been adapted
    from the model described in Cox, S.J.D, & Richard, S.M. (2005) A formal model for the geologic timescale and GSSP,
    compatible with geospatial information transfer standards, Geosphere, Geological Society of America.
    """
    ontology_id = "GTS"
    ontology_full_name = "Geologic Timescale model (GTS)"
    domain = "Geography"
    category = "geospatial Information, Geology"
    version = "1.0"
    last_updated = "2020-05-31"
    creator = "Simon J D Cox (<EMAIL>) of CSIRO"
    license = "Creative Commons 1.0"
    format = "TTL"
    download_url = "https://raw.githack.com/CGI-IUGS/timescale-ont/master/html/gts.html"


class Juso(BaseOntology):
    """
    Juso Ontology is a Web vocabulary for describing geographical addresses and features.
    """
    ontology_id = "Juso"
    ontology_full_name = "Juso Ontology (Juso)"
    domain = "Geography"
    category = "geographical knowledge"
    version = "0.1.1"
    last_updated = "2015-11-10"
    creator = "James G. Kim, LiST Inc."
    license = "Creative Commons 4.0"
    format = "TTL"
    download_url = "https://rdfs.co/juso/0.1.1/html"
