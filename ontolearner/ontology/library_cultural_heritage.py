# Copyright (c) 2025 SciKnowOrg
#
# Permission is hereby granted, free of charge, to any person obtaining a copy
# of this software and associated documentation files (the "Software"), to deal
# in the Software without restriction, including without limitation the rights
# to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
# copies of the Software, and to permit persons to whom the Software is
# furnished to do so, subject to the following conditions:
#
# The above copyright notice and this permission notice shall be included in all
# copies or substantial portions of the Software.
#
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
# IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
# FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
# AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
# LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
# OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
# SOFTWARE.

from ontolearner.base import BaseOntology


class GND(BaseOntology):
    """
    GND stands for Gemeinsame Normdatei (Integrated Authority File) and offers a broad range of elements
    to describe authorities. The GND originates from the German library community and aims
    to solve the name ambiguity problem in the library world.
    """
    ontology_id = "GND"
    ontology_full_name = "Gemeinsame Normdatei (GND)"
    domain = "Library and Cultural Heritage"
    category = "Authority Files"
    version = "1.2.0"
    last_updated = "2024-08-26"
    creator = "Alexander Haffner"
    license = "Creative Commons 1.0"
    format = "RDF"
    download_url = "https://d-nb.info/standards/elementset/gnd"
