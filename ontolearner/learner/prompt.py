# Copyright (c) 2025 SciKnowOrg
#
# Permission is hereby granted, free of charge, to any person obtaining a copy
# of this software and associated documentation files (the "Software"), to deal
# in the Software without restriction, including without limitation the rights
# to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
# copies of the Software, and to permit persons to whom the Software is
# furnished to do so, subject to the following conditions:
#
# The above copyright notice and this permission notice shall be included in all
# copies or substantial portions of the Software.
#
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
# IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
# FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
# AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
# LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
# OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
# SOFTWARE.

from ..base import AutoPrompt


class StandardizedPrompting(AutoPrompt):
    def __init__(self, prompt_template: str = None, task: str = None):
        if prompt_template is None:
            if task == "term-typing":
                prompt_template = """
Given a list of types as candidates to be assigned to the term, identify the most probable types.
Return the types only in the form of a list. Do not provide any explanation outside the list.

Term:
{term}

Candidates Types:
{context}

Response:
"""
            elif task == "taxonomy-discovery":
                prompt_template = """
Is {parent} a parent of {child}?
Answer yes/no. Do not explain.
"""
            elif task == "task-non-taxonomic-relations":
                prompt_template = """
What is the relation between {head} and {tail}? Return only the relation type.
"""

        super().__init__(prompt_template)

    def format(self, **kwargs):
        return self.prompt_template.format(**kwargs)
