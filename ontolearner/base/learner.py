# Copyright (c) 2025 SciKnowOrg
#
# Permission is hereby granted, free of charge, to any person obtaining a copy
# of this software and associated documentation files (the "Software"), to deal
# in the Software without restriction, including without limitation the rights
# to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
# copies of the Software, and to permit persons to whom the Software is
# furnished to do so, subject to the following conditions:
#
# The above copyright notice and this permission notice shall be included in all
# copies or substantial portions of the Software.
#
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
# IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
# FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
# AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
# LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
# OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
# SOFTWARE.

from abc import ABC
from typing import Any, List


class AutoLearner(ABC):

    def __init__(self, **kwargs):
        pass

    def load(self, **kwargs):
        pass

    def fit(self, train_data: Any, task: str):
        pass

    def predict(self, eval_data: Any, task: str) -> Any:
        pass

    def fit_predict(self, train_data: Any, eval_data: Any, task: str) -> Any:
        self.fit(train_data, task)
        predicts = self.predict(eval_data, task)
        return predicts


class AutoLLM(ABC):
    def __init__(self):
        self.model = None
        self.tokenizer = None

    def load(self, model_id: str):
        pass

    def generate(self, inputs: List[Any]) -> List[Any]:
        pass


class AutoRetriever(ABC):
    def __init__(self):
        self.model = None

    def load(self, model_id: str):
        pass

    def index(self, inputs: List[Any]):
        pass

    def retrieve(self, inputs: List[Any], top_k: int) -> List[Any]:
        pass


class AutoPrompt(ABC):
    def __init__(self, prompt_template: str):
        self.prompt_template = prompt_template

    def format(self, **kwargs):
        pass
