# Copyright (c) 2023-2025 Scientific Knowledge Organization (SciKnowOrg)
#
# Permission is hereby granted, free of charge, to any person obtaining a copy
# of this software and associated documentation files (the "Software"), to deal
# in the Software without restriction, including without limitation the rights
# to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
# copies of the Software, and to permit persons to whom the Software is
# furnished to do so, subject to the following conditions:
#
# The above copyright notice and this permission notice shall be included in all
# copies or substantial portions of the Software.
#
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
# IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
# FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
# AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
# LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
# OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
# SOFTWARE.

from ..base import BaseOntology


class GoodRelations(BaseOntology):
    """
    GoodRelations is a standardized vocabulary (also known as "schema", "data dictionary",
    or "ontology") for product, price, store, and company data that can (1) be embedded
    into existing static and dynamic Web pages and that (2) can be processed by other computers.
    This increases the visibility of your products and services in the latest generation
    of search engines, recommender systems, and other novel applications.
    """
    ontology_id = "GoodRelations"
    ontology_full_name = "Good Relations Language Reference (GoodRelations)"
    domain = "Finance"
    category = "E-commerce"
    version = "1.0"
    last_updated = "2011-10-01"
    creator = "Martin Hepp"
    license = "Creative Commons 3.0"
    format = "OWL"
    download_url = "https://www.heppnetz.de/ontologies/goodrelations/v1"
