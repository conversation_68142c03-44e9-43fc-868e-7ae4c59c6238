# Copyright (c) 2025 SciKnowOrg
#
# Permission is hereby granted, free of charge, to any person obtaining a copy
# of this software and associated documentation files (the "Software"), to deal
# in the Software without restriction, including without limitation the rights
# to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
# copies of the Software, and to permit persons to whom the Software is
# furnished to do so, subject to the following conditions:
#
# The above copyright notice and this permission notice shall be included in all
# copies or substantial portions of the Software.
#
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
# IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
# FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
# AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
# LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
# OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
# SOFTWARE.

import numpy as np
from typing import List, Dict, Any


def aggregate_metrics(results: List[Dict[str, Any]], task: str) -> Dict[str, float]:
    """
    Aggregate metrics across multiple examples
    """
    metrics = {}

    if not results:
        return metrics

    if task == "term-typing":
        metrics['avg_precision_score'] = np.mean([r['precision_score'] for r in results])
        metrics['avg_recall_score'] = np.mean([r['recall_score'] for r in results])
        metrics['avg_f1_score'] = np.mean([r['f1_score'] for r in results])
        metrics['avg_exact_match'] = np.mean([r['exact_match'] for r in results])

    elif task == "taxonomy-discovery":
        metrics['avg_accuracy'] = np.mean([r['accuracy'] for r in results])
        metrics['avg_f1_score'] = np.mean([r['f1_score'] for r in results])

    elif task == "non-taxonomy-discovery":
        metrics['avg_exact_match'] = np.mean([r['exact_match'] for r in results])
        metrics['avg_similarity'] = np.mean([r['similarity_score'] for r in results])

    metrics['num_samples'] = len(results)

    return metrics
