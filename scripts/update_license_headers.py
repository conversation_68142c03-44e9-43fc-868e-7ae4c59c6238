# Copyright (c) 2025 SciKnowOrg
#
# Permission is hereby granted, free of charge, to any person obtaining a copy
# of this software and associated documentation files (the "Software"), to deal
# in the Software without restriction, including without limitation the rights
# to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
# copies of the Software, and to permit persons to whom the Software is
# furnished to do so, subject to the following conditions:
#
# The above copyright notice and this permission notice shall be included in all
# copies or substantial portions of the Software.
#
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
# IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
# FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
# AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
# LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
# OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
# SOFTWARE.

"""
<PERSON><PERSON><PERSON> to update MIT license headers in Python files to match the LICENSE file exactly.

This script finds Python files with existing license headers and updates them to match
the exact format and copyright information from the LICENSE file.
"""

import os
import logging
import re
from pathlib import Path
from typing import List

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Correct MIT License header (matches LICENSE file exactly)
CORRECT_MIT_HEADER = '''# Copyright (c) 2025 SciKnowOrg
#
# Permission is hereby granted, free of charge, to any person obtaining a copy
# of this software and associated documentation files (the "Software"), to deal
# in the Software without restriction, including without limitation the rights
# to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
# copies of the Software, and to permit persons to whom the Software is
# furnished to do so, subject to the following conditions:
#
# The above copyright notice and this permission notice shall be included in all
# copies or substantial portions of the Software.
#
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
# IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
# FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
# AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
# LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
# OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
# SOFTWARE.

'''


def find_license_header_end(content: str) -> int:
    """
    Find the end of the license header in file content.
    
    Args:
        content (str): File content
        
    Returns:
        int: Index where license header ends, or 0 if no header found
    """
    lines = content.split('\n')
    header_end = 0
    
    # Look for the end of the license header
    for i, line in enumerate(lines):
        if line.strip().startswith('#') and ('Copyright' in line or 'Permission' in line or 'SOFTWARE' in line):
            header_end = i + 1
        elif line.strip().startswith('#') and line.strip() != '#':
            header_end = i + 1
        elif line.strip() == '#':
            header_end = i + 1
        elif line.strip() == '' and header_end > 0:
            # Empty line after license header
            header_end = i + 1
            break
        elif header_end > 0 and not line.strip().startswith('#'):
            # Non-comment line after license header
            break
    
    return header_end


def update_license_header(file_path: Path) -> bool:
    """
    Update the license header in a Python file.
    
    Args:
        file_path (Path): Path to the Python file
        
    Returns:
        bool: True if header was updated successfully, False otherwise
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            original_content = f.read()
        
        # Check if file has a license header
        if 'Copyright' not in original_content[:1000]:
            logger.debug(f"No license header found in {file_path}")
            return False
        
        # Find where the license header ends
        header_end_line = find_license_header_end(original_content)
        
        if header_end_line == 0:
            logger.debug(f"Could not determine license header end in {file_path}")
            return False
        
        # Split content into lines
        lines = original_content.split('\n')
        
        # Keep everything after the license header
        remaining_content = '\n'.join(lines[header_end_line:])
        
        # Remove leading empty lines from remaining content
        remaining_content = remaining_content.lstrip('\n')
        
        # Combine new header with remaining content
        new_content = CORRECT_MIT_HEADER + remaining_content
        
        # Write updated content
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        logger.info(f"Updated license header in {file_path}")
        return True
        
    except Exception as e:
        logger.error(f"Error updating license header in {file_path}: {e}")
        return False


def find_python_files(directory: Path) -> List[Path]:
    """
    Find all Python files in a directory recursively.
    
    Args:
        directory (Path): Directory to search
        
    Returns:
        List[Path]: List of Python file paths
    """
    python_files = []
    for root, dirs, files in os.walk(directory):
        # Skip certain directories
        dirs[:] = [d for d in dirs if d not in ['.git', '__pycache__', '.pytest_cache', 'build', 'dist']]
        
        for file in files:
            if file.endswith('.py'):
                python_files.append(Path(root) / file)
    
    return python_files


def main():
    """Main function to update license headers in Python files."""
    # Get the project root directory
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    
    # Find all Python files in the ontolearner package
    ontolearner_dir = project_root / 'ontolearner'
    if not ontolearner_dir.exists():
        logger.error(f"OntoLearner directory not found: {ontolearner_dir}")
        return
    
    python_files = find_python_files(ontolearner_dir)
    logger.info(f"Found {len(python_files)} Python files in {ontolearner_dir}")
    
    # Also update scripts
    scripts_dir = project_root / 'scripts'
    if scripts_dir.exists():
        script_files = find_python_files(scripts_dir)
        python_files.extend(script_files)
        logger.info(f"Found {len(script_files)} additional Python files in {scripts_dir}")
    
    # Process each file
    files_updated = 0
    files_skipped = 0
    
    for file_path in python_files:
        if update_license_header(file_path):
            files_updated += 1
        else:
            files_skipped += 1
    
    logger.info(f"Processing complete:")
    logger.info(f"  Files updated: {files_updated}")
    logger.info(f"  Files skipped: {files_skipped}")
    logger.info(f"  Total files: {len(python_files)}")


if __name__ == "__main__":
    main()
